import math
import numpy as np
from rlbot.agents.base_agent import SimpleControllerState
from rlbot.utils.structures.game_data_struct import GameTickPacket, FieldInfoPacket, PlayerInfo, Physics, Vector3, Rotator
import time

BLUE_TEAM = 0
ORANGE_TEAM = 1
BLUE_GOAL_CENTER = np.array([0, -5120, 321.3875])
ORANGE_GOAL_CENTER = np.array([0, 5120, 321.3875])
GOAL_WIDTH = 892.755 * 2

class PhysicsObject:
    def __init__(self):
        self.position: np.ndarray = np.zeros(3)
        self.linear_velocity: np.ndarray = np.zeros(3)
        self.angular_velocity: np.ndarray = np.zeros(3)
        self._euler_angles: np.ndarray = np.zeros(3)
        self._rotation_mtx: np.ndarray = np.zeros((3,3))
        self._has_computed_rot_mtx = False
    def decode_car_data(self, car_data: Physics):
        self.position = self._vector_to_numpy(car_data.location)
        self._euler_angles = self._rotator_to_numpy(car_data.rotation)
        self.linear_velocity = self._vector_to_numpy(car_data.velocity)
        self.angular_velocity = self._vector_to_numpy(car_data.angular_velocity)
        self._has_computed_rot_mtx = False
    def decode_ball_data(self, ball_data: Physics):
        self.position = self._vector_to_numpy(ball_data.location)
        self.linear_velocity = self._vector_to_numpy(ball_data.velocity)
        self.angular_velocity = self._vector_to_numpy(ball_data.angular_velocity)
    def _vector_to_numpy(self, vector: Vector3): return np.asarray([vector.x, vector.y, vector.z])
    def _rotator_to_numpy(self, rotator: Rotator): return np.asarray([rotator.pitch, rotator.yaw, rotator.roll])

class PlayerData:
    def __init__(self):
        self.car_id: int = -1
        self.team_num: int = -1
        self.is_demoed: bool = False
        self.boost_amount: float = -1
        self.car_data: PhysicsObject = PhysicsObject()

class GameState:
    def __init__(self, field_info: FieldInfoPacket):
        self.field_info = field_info
        self.players: list[PlayerData] = []
        self.ball: PhysicsObject = PhysicsObject()
        self.teammates: list[PlayerData] = []
        self.opponents: list[PlayerData] = []
    def decode(self, packet: GameTickPacket, local_player_index: int):
        self.ball.decode_ball_data(packet.game_ball.physics)
        self.players = []
        for i in range(packet.num_cars):
            self.players.append(self._decode_player(packet.game_cars[i], i))
        if local_player_index < len(self.players):
            local_player_team = self.players[local_player_index].team_num
            self.teammates = [p for p in self.players if p.team_num == local_player_team and p.car_id != local_player_index]
            self.opponents = [p for p in self.players if p.team_num != local_player_team]
    def _decode_player(self, player_info: PlayerInfo, index: int) -> PlayerData:
        p_data = PlayerData()
        p_data.car_id = index
        p_data.team_num = player_info.team
        p_data.is_demoed = player_info.is_demolished
        p_data.boost_amount = player_info.boost / 100
        p_data.car_data.decode_car_data(player_info.physics)
        return p_data
    def get_player(self, index: int) -> PlayerData|None:
        return next((p for p in self.players if p.car_id == index), None)

class RotationManager:
    def __init__(self, ConsoleLogger=print):
        self.logger = ConsoleLogger
        self.Name = lambda: "RotationManagerPlugin"
        self.last_role = None

    def assign_roles(self, state: GameState, local_player_index: int):
        # Assign roles based on distance to ball
        all_team = [p for p in state.players if p.team_num == state.players[local_player_index].team_num]
        ball_pos = state.ball.position
        dists = [(p.car_id, np.linalg.norm(p.car_data.position - ball_pos)) for p in all_team]
        dists.sort(key=lambda x: x[1])
        roles = {}
        for i, (car_id, _) in enumerate(dists):
            if i == 0:
                roles[car_id] = "FIRST"
            elif i == 1:
                roles[car_id] = "SECOND"
            else:
                roles[car_id] = "THIRD"
        return roles

    def get_back_post_target(self, player: PlayerData, ball_pos):
        # Returns a back post position for rotation
        own_goal = ORANGE_GOAL_CENTER if player.team_num else BLUE_GOAL_CENTER
        # Determine which post is further from the ball
        left_post_x = -GOAL_WIDTH/2 + 100
        right_post_x = GOAL_WIDTH/2 - 100
        if abs(ball_pos[0] - left_post_x) > abs(ball_pos[0] - right_post_x):
            back_post = np.array([left_post_x, own_goal[1], 0])
        else:
            back_post = np.array([right_post_x, own_goal[1], 0])
        return back_post

    def get_action(self, state: GameState, local_player_index: int, roles):
        player = state.get_player(local_player_index)
        role = roles.get(local_player_index, "NONE")
        ball_pos = state.ball.position
        # 3v3 or 2v2
        num_team = len([p for p in state.players if p.team_num == player.team_num])
        # Default: do nothing
        target = player.car_data.position
        action = "IDLE"
        if role == "FIRST":
            # Challenge the ball
            target = ball_pos
            action = "CHALLENGE"
        elif role == "SECOND":
            # Support: predict outcome, be ready to follow up
            # Predict ball after 0.5s
            predicted = ball_pos + state.ball.linear_velocity * 0.5
            # Stay a bit behind the ball
            offset = np.array([0, -300 if player.team_num == 0 else 300, 0])
            target = predicted + offset
            action = "SUPPORT"
        elif role == "THIRD" and num_team == 3:
            # Anchor: stay back, cover midfield or behind
            own_goal = ORANGE_GOAL_CENTER if player.team_num else BLUE_GOAL_CENTER
            target = (ball_pos + own_goal) / 2
            action = "ANCHOR"
        # Back post rotation if rotating back
        if action != "CHALLENGE":
            # If behind the ball, rotate back post
            if (player.team_num == 0 and player.car_data.position[1] < ball_pos[1]) or (player.team_num == 1 and player.car_data.position[1] > ball_pos[1]):
                target = self.get_back_post_target(player, ball_pos)
                action = "BACK_POST_ROTATE"
        # Prefer small boost pads (not implemented: just a note for future extension)
        # Output debug info
        self.logger(f"[{self.Name()}] Role: {role}, Action: {action}, Target: {target}")
        return target, action

class RotationManagerPlugin:
    def __init__(self, ConsoleLogger=print):
        self.logger = ConsoleLogger
        self.Name = lambda: "RotationManagerPlugin"
        self.active = True
        self.game_state: GameState = None
        self.player_index = -1
        self.intervening = False
        self.target_pos = None
        self.last_intervention_end_time = 0.0
        self.cooldown_duration = 2.0  # seconds
        self.last_roles_printed = None  # Store last printed roles

    def initialize(self):
        self.logger(f"[{self.Name()}] Initialized.")

    def _is_kickoff(self, packet: GameTickPacket) -> bool:
        if hasattr(packet.game_info, 'is_kickoff_pause') and packet.game_info.is_kickoff_pause:
            return True
        if hasattr(packet, 'game_ball') and hasattr(packet.game_ball, 'physics'):
            ball_phys = packet.game_ball.physics
            at_center = (abs(ball_phys.location.x) < 10 and abs(ball_phys.location.y) < 10)
            low_velocity = (abs(ball_phys.velocity.x) < 10 and abs(ball_phys.velocity.y) < 10 and abs(ball_phys.velocity.z) < 10)
            is_round_active = hasattr(packet.game_info, 'is_round_active') and packet.game_info.is_round_active
            if at_center and low_velocity and is_round_active:
                return True
        return False

    def _get_back_post(self, player: PlayerData, ball_pos):
        own_goal = ORANGE_GOAL_CENTER if player.team_num else BLUE_GOAL_CENTER
        left_post_x = -GOAL_WIDTH/2 + 100
        right_post_x = GOAL_WIDTH/2 - 100
        left_post = np.array([left_post_x, own_goal[1], 0])
        right_post = np.array([right_post_x, own_goal[1], 0])
        car_pos = player.car_data.position
        # Pick the post closest to the car
        if np.linalg.norm(car_pos - left_post) < np.linalg.norm(car_pos - right_post):
            return left_post
        else:
            return right_post

    def game_tick_packet_set(self, packet: GameTickPacket, local_player_index: int, playername: str, field_info: FieldInfoPacket=None) -> SimpleControllerState|None:
        if field_info and self.game_state is None:
            self.game_state = GameState(field_info)
        if self.game_state is None:
            return None
        if self._is_kickoff(packet):
            self.intervening = False
            self.last_intervention_end_time = time.time()
            return None
        self.player_index = local_player_index
        self.game_state.decode(packet, self.player_index)
        self.assign_and_print_roles(packet, self.player_index)
        player = self.game_state.get_player(self.player_index)
        if player is None:
            self.intervening = False
            self.last_intervention_end_time = time.time()
            return None
        ball_pos = self.game_state.ball.position
        car_pos = player.car_data.position
        # Determine if ball is in our half
        our_side = (player.team_num == 0 and ball_pos[1] < 0) or (player.team_num == 1 and ball_pos[1] > 0)
        # Calculate back post
        back_post = self._get_back_post(player, ball_pos)
        dist_to_back_post = np.linalg.norm(car_pos - back_post)
        now = time.time()
        # If we are in cooldown, do not intervene
        if not self.intervening and now - self.last_intervention_end_time < self.cooldown_duration:
            return None
        # If at least one enemy is on our side and we are between the enemy, the ball, and the goal, stop intervening
        if self._check_acting_as_last_defender(player, ball_pos):
            return None
        # If the ball is on our side and moving toward our net, stop intervening and return control
        if self._check_ball_moving_toward_net(player, ball_pos):
            return None
        # If ball is in our half and we are not near back post, intervene
        if our_side and dist_to_back_post > 700:
            # If we are very close to the ball, stop intervening and return control
            dist_to_ball = np.linalg.norm(car_pos - ball_pos)
            if dist_to_ball < 450:
                if self.intervening:
                    self.logger(f"[{self.Name()}] Ball is very close ({dist_to_ball:.0f}u), returning control to Opti.")
                self.intervening = False
                self.last_intervention_end_time = time.time()
                return None
            if not self.intervening:
                self.logger(f"[{self.Name()}] Intervening: Driving to back post!")
            self.intervening = True
            self.target_pos = back_post
            to_target = back_post - car_pos
            dist = np.linalg.norm(to_target)
            car_yaw = player.car_data._euler_angles[1]  # Yaw is the second element
            target_angle = math.atan2(to_target[1], to_target[0])
            angle_diff = target_angle - car_yaw
            while angle_diff > math.pi:
                angle_diff -= 2 * math.pi
            while angle_diff < -math.pi:
                angle_diff += 2 * math.pi
            speed = np.linalg.norm(player.car_data.linear_velocity)
            controls = SimpleControllerState()
            controls.steer = np.clip(angle_diff * 3.0, -1, 1)
            # Throttle logic for smooth arrival
            if dist > 700:
                controls.throttle = 1.0
            elif dist > 400:
                controls.throttle = 0.3
            else:
                controls.throttle = 0.0
                controls.handbrake = True
            if dist > 1200 and abs(angle_diff) < 0.3 and player.boost_amount > 0 and speed < 2200:
                controls.boost = True
            # Before rotating back to back post, check if we can win the challenge to the ball
            # Only rotate back if we are NOT the best candidate to challenge
            my_dist = np.linalg.norm(car_pos - ball_pos)
            my_boost = player.boost_amount
            can_win_challenge = True
            for opp in self.game_state.opponents:
                opp_pos = opp.car_data.position
                opp_boost = opp.boost_amount
                opp_dist = np.linalg.norm(opp_pos - ball_pos)
                # If any opponent is closer or has more boost (and is close in distance), we can't win
                if opp_dist < my_dist - 100 or (opp_dist < my_dist + 200 and opp_boost > my_boost + 0.1):
                    can_win_challenge = False
                    break
            if can_win_challenge:
                if self.intervening:
                    self.logger(f"[{self.Name()}] We can win the challenge, returning control to Opti.")
                self.intervening = False
                self.last_intervention_end_time = time.time()
                return None
            # If we are now close to back post, stop intervening and set cooldown
            if dist < 400:
                self.logger(f"[{self.Name()}] Arrived at back post, returning control to Opti.")
                self.intervening = False
                self.last_intervention_end_time = time.time()
            # If we are already at the back post, stop intervening and return control
            if dist_to_back_post < 400:
                if self.intervening:
                    self.logger(f"[{self.Name()}] Already at back post, returning control to Opti.")
                self.intervening = False
                self.last_intervention_end_time = time.time()
                return None
            return controls
        else:
            if self.intervening:
                self.logger(f"[{self.Name()}] Situation resolved, returning control to Opti.")
                self.intervening = False
                self.last_intervention_end_time = time.time()
            return None

    def shutdown(self):
        self.logger(f"[{self.Name()}] Shutdown.")

    # If at least one enemy is on our side and we are between the enemy, the ball, and the goal, stop intervening
    def _check_acting_as_last_defender(self, player: PlayerData, ball_pos):
        car_pos = player.car_data.position
        own_goal = ORANGE_GOAL_CENTER if player.team_num else BLUE_GOAL_CENTER
        for opp in self.game_state.opponents:
            opp_pos = opp.car_data.position
            # Check if enemy is on our side
            if (player.team_num == 0 and opp_pos[1] < 0) or (player.team_num == 1 and opp_pos[1] > 0):
                # Check if we are between enemy and ball and goal (projected on Y axis)
                # We are between if our Y is between the enemy and the goal, and the ball is further upfield than the enemy
                if (player.team_num == 0 and own_goal[1] < car_pos[1] < opp_pos[1]) or (player.team_num == 1 and own_goal[1] > car_pos[1] > opp_pos[1]):
                    # Also check that the ball is further upfield than the enemy
                    if (player.team_num == 0 and ball_pos[1] > opp_pos[1]) or (player.team_num == 1 and ball_pos[1] < opp_pos[1]):
                        if self.intervening:
                            self.logger(f"[{self.Name()}] Acting as last defender, returning control to Opti.")
                        self.intervening = False
                        self.last_intervention_end_time = time.time()
                        return True
        return False

    # If the ball is on our side and moving toward our net, stop intervening and return control
    def _check_ball_moving_toward_net(self, player: PlayerData, ball_pos):
        goal_y = 5120 if player.team_num == 1 else -5120
        ball_on_our_side = (player.team_num == 0 and ball_pos[1] < 0) or (player.team_num == 1 and ball_pos[1] > 0)
        ball_vel = self.game_state.ball.linear_velocity
        ball_moving_toward_net = (player.team_num == 0 and ball_vel[1] < -100) or (player.team_num == 1 and ball_vel[1] > 100)
        if ball_on_our_side and ball_moving_toward_net:
            if self.intervening:
                self.logger(f"[{self.Name()}] Ball is moving toward our net, returning control to Opti.")
            self.intervening = False
            self.last_intervention_end_time = time.time()
            return True
        return False

    # Assign and print roles for all players at the start of each tick
    def assign_and_print_roles(self, packet: GameTickPacket, local_player_index: int):
        if self.game_state is None:
            return
        player = self.game_state.get_player(local_player_index)
        if player is None:
            return
        num_team = len([p for p in self.game_state.players if p.team_num == player.team_num])
        ball_pos = self.game_state.ball.position
        team_players = [p for p in self.game_state.players if p.team_num == player.team_num]
        dists = [(p.car_id, np.linalg.norm(p.car_data.position - ball_pos)) for p in team_players]
        dists.sort(key=lambda x: x[1])
        roles = {}
        for i, (car_id, _) in enumerate(dists):
            if num_team == 1:
                roles[car_id] = "SOLO"
            elif num_team == 2:
                roles[car_id] = "FIRST" if i == 0 else "SECOND"
            else:
                if i == 0:
                    roles[car_id] = "FIRST"
                elif i == 1:
                    roles[car_id] = "SECOND"
                else:
                    roles[car_id] = "THIRD"
        # Only print if roles changed
        if roles != self.last_roles_printed:
            # Try to get player names from packet
            names = {}
            if hasattr(packet, 'game_cars'):
                for i, car in enumerate(packet.game_cars):
                    if hasattr(car, 'name'):
                        names[i] = car.name
            role_str = ", ".join([f"{names.get(car_id, f'Player {car_id}')}: {role}" for car_id, role in roles.items()])
            self.logger(f"[{self.Name()}] Roles this tick: {role_str}")
            self.last_roles_printed = roles.copy()

        # If both we and at least one teammate are on the enemy side, check if we are first man; if not, rotate back
        car_pos = player.car_data.position
        back_post = self._get_back_post(player, ball_pos)
        enemy_side = (player.team_num == 0 and car_pos[1] > 0) or (player.team_num == 1 and car_pos[1] < 0)
        teammates = self.game_state.teammates
        teammate_on_enemy_side = any((player.team_num == 0 and tm.car_data.position[1] > 0) or (player.team_num == 1 and tm.car_data.position[1] < 0) for tm in teammates)
        if enemy_side and teammate_on_enemy_side:
            # Determine if we are first man (closest to ball among teammates and self)
            my_dist = np.linalg.norm(car_pos - ball_pos)
            teammate_dists = [np.linalg.norm(tm.car_data.position - ball_pos) for tm in teammates]
            if any(td < my_dist for td in teammate_dists):
                # Not first man, force rotate back
                if not self.intervening:
                    self.logger(f"[{self.Name()}] Not first man on enemy side, rotating back to post.")
                self.intervening = True
                self.target_pos = back_post
                # (rest of intervention logic will execute as normal) 